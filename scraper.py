import asyncio
import pandas as pd
from playwright.async_api import async_playwright, Page

class XiaohongshuScraper:
    def __init__(self):
        self.browser = None
        self.page = None

    async def initialize(self):
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()

    async def close(self):
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def scrape_user_posts(self, url: str, progress_callback=None, log_callback=None):
        if not self.page:
            await self.initialize()

        log_callback("正在导航到登录页面...")
        await self.page.goto("https://www.xiaohongshu.com/login")
        log_callback("请在弹出的浏览器中手动扫码登录小红书。登录成功后，请关闭登录页面。")
        
        # 等待用户手动登录，这里需要用户手动操作
        # 可以通过检查某个登录后的元素是否存在来判断是否登录成功
        # 暂时先简单等待一段时间，实际应用中需要更智能的判断
        await self.page.wait_for_timeout(30000) # 等待30秒让用户登录

        log_callback(f"正在导航到用户主页: {url}")
        await self.page.goto(url)
        await self.page.wait_for_load_state('networkidle')

        posts_data = []
        seen_post_urls = set()

        log_callback("开始滚动页面并提取帖子链接...")
        scroll_count = 0
        while True:
            # 滚动到底部
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self.page.wait_for_timeout(2000) # 等待页面加载

            # 提取当前页面所有帖子链接
            post_elements = await self.page.query_selector_all("div.note-item a")
            current_page_urls = [await el.get_attribute("href") for el in post_elements]
            
            new_urls = [u for u in current_page_urls if u not in seen_post_urls]
            if not new_urls:
                log_callback("没有新的帖子链接，可能已滚动到底部或加载完毕。")
                break # 没有新的链接，说明已经到底部

            for u in new_urls:
                seen_post_urls.add(u)
                posts_data.append({"url": f"https://www.xiaohongshu.com{u}"})
            
            log_callback(f"已发现 {len(seen_post_urls)} 个帖子链接。")
            if progress_callback:
                progress_callback(len(seen_post_urls))
            
            scroll_count += 1
            if scroll_count > 50: # 设置一个最大滚动次数，防止无限循环
                log_callback("达到最大滚动次数，停止滚动。")
                break

        log_callback(f"共找到 {len(posts_data)} 个帖子链接。开始解析每个帖子...")

        for i, post in enumerate(posts_data):
            try:
                log_callback(f"正在解析帖子: {post['url']}")
                await self.page.goto(post['url'])
                await self.page.wait_for_load_state('networkidle')

                title_element = await self.page.query_selector("h1.title")
                post['title'] = await title_element.inner_text() if title_element else "N/A"

                content_element = await self.page.query_selector("div.content")
                post['content'] = await content_element.inner_text() if content_element else "N/A"

                # 提取图片URL
                image_elements = await self.page.query_selector_all("div.swiper-slide img")
                post['images'] = [await img.get_attribute("src") for img in image_elements] if image_elements else []

                # 提取点赞、评论、收藏数 (需要根据实际页面元素调整选择器)
                likes_element = await self.page.query_selector("span.count-text") # 示例选择器，可能需要调整
                post['likes'] = await likes_element.inner_text() if likes_element else "0"

                # 评论数和收藏数可能需要更复杂的选择器或通过API获取
                post['comments'] = "N/A"
                post['collections'] = "N/A"

                if progress_callback:
                    progress_callback(len(posts_data) + i + 1) # 更新总进度

            except Exception as e:
                log_callback(f"解析帖子 {post['url']} 时发生错误: {e}")
                post['error'] = str(e)
            
        df = pd.DataFrame(posts_data)
        return df

if __name__ == "__main__":
    async def main():
        scraper = XiaohongshuScraper()
        try:
            await scraper.initialize()
            # 替换为你要爬取的用户主页URL
            user_url = "https://www.xiaohongshu.com/user/profile/5e7b3b3b0000000001000000" 
            
            def print_progress(count):
                print(f"进度: {count} 个帖子已处理")

            def print_log(message):
                print(f"日志: {message}")

            df = await scraper.scrape_user_posts(user_url, print_progress, print_log)
            output_path = "output/xiaohongshu_posts.csv"
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {output_path}")
        except Exception as e:
            print(f"发生错误: {e}")
        finally:
            await scraper.close()

    asyncio.run(main())
