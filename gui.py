import sys
import asyncio
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QTextEdit, QLabel, QProgressBar
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from scraper import XiaohongshuScraper

class ScraperThread(QThread):
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    finished = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, url):
        super().__init__()
        self.url = url
        self.scraper = XiaohongshuScraper()

    async def run_scraper(self):
        try:
            await self.scraper.initialize()
            df = await self.scraper.scrape_user_posts(
                self.url,
                progress_callback=self.progress_updated.emit,
                log_callback=self.log_updated.emit
            )
            output_path = "output/xiaohongshu_posts.csv"
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            self.finished.emit(f"数据已保存到 {output_path}")
        except Exception as e:
            self.error.emit(f"爬取过程中发生错误: {e}")
        finally:
            await self.scraper.close()

    def run(self):
        asyncio.run(self.run_scraper())

class XiaohongshuScraperGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("小红书爬虫")
        self.setGeometry(100, 100, 800, 600)
        self.init_ui()
        self.scraper_thread = None

    def init_ui(self):
        main_layout = QVBoxLayout()

        # URL输入部分
        url_layout = QHBoxLayout()
        url_label = QLabel("用户主页URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("请输入小红书用户主页URL")
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input)
        main_layout.addLayout(url_layout)

        # 按钮部分
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("开始爬取")
        self.start_button.clicked.connect(self.start_scraping)
        self.stop_button = QPushButton("停止爬取")
        self.stop_button.clicked.connect(self.stop_scraping)
        self.stop_button.setEnabled(False) # 初始禁用停止按钮
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)

        # 进度条和状态标签
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("进度: %p%")
        main_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("状态: 待机")
        main_layout.addWidget(self.status_label)

        # 日志显示区
        log_label = QLabel("日志输出:")
        main_layout.addWidget(log_label)
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        main_layout.addWidget(self.log_output)

        self.setLayout(main_layout)

    def start_scraping(self):
        url = self.url_input.text()
        if not url:
            self.log_output.append("请输入用户主页URL！")
            return

        self.log_output.clear()
        self.log_output.append("爬取任务开始...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("状态: 正在初始化...")

        self.scraper_thread = ScraperThread(url)
        self.scraper_thread.progress_updated.connect(self.update_progress)
        self.scraper_thread.log_updated.connect(self.update_log)
        self.scraper_thread.finished.connect(self.scraping_finished)
        self.scraper_thread.error.connect(self.scraping_error)
        self.scraper_thread.start()

    def stop_scraping(self):
        if self.scraper_thread and self.scraper_thread.isRunning():
            self.scraper_thread.terminate() # 强制终止线程
            self.scraper_thread.wait() # 等待线程结束
            self.log_output.append("爬取任务已停止。")
            self.status_label.setText("状态: 已停止")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_log(self, message):
        self.log_output.append(message)
        self.log_output.verticalScrollBar().setValue(self.log_output.verticalScrollBar().maximum()) # 自动滚动到底部

    def scraping_finished(self, message):
        self.log_output.append(message)
        self.status_label.setText("状态: 完成")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100)

    def scraping_error(self, message):
        self.log_output.append(message)
        self.status_label.setText("状态: 错误")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = XiaohongshuScraperGUI()
    window.show()
    sys.exit(app.exec())
